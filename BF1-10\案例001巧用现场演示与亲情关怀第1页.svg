<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: 600; fill: #34495e; }
      .content { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: 600; fill: #e74c3c; }
      .section-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="60" y="60" width="1800" height="120" rx="10" class="section-bg"/>
  <text x="960" y="130" text-anchor="middle" class="title">案例一：巧用现场演示与亲情关怀</text>
  <text x="960" y="170" text-anchor="middle" class="subtitle">攻克农村康养套餐销售难题</text>
  
  <!-- 客户背景 -->
  <rect x="60" y="220" width="880" height="380" rx="10" class="section-bg"/>
  <text x="80" y="260" class="subtitle">客户背景</text>
  <text x="80" y="310" class="content">本案例包含两个独立的目标客户，均为农村地区的居民。</text>
  
  <text x="80" y="360" class="highlight">客户一：</text>
  <text x="80" y="390" class="content">主动上门咨询业务的村民，但初期对康养服务及安防</text>
  <text x="80" y="420" class="content">监控没有明确需求。</text>
  
  <text x="80" y="470" class="highlight">客户二：</text>
  <text x="80" y="500" class="content">一位独居在家的老年人，有高血压病史，家庭经济</text>
  <text x="80" y="530" class="content">条件一般。其子女均在外地工作，老人使用的还是</text>
  <text x="80" y="560" class="content">一部老旧的2G手机，已无法正常通话。</text>
  
  <!-- 痛点分析 -->
  <rect x="980" y="220" width="880" height="380" rx="10" class="section-bg"/>
  <text x="1000" y="260" class="subtitle">痛点分析</text>
  
  <text x="1000" y="310" class="highlight">客户一的痛点：</text>
  <text x="1000" y="340" class="content">"无感知"，即不了解产品价值，认为自身没有相关</text>
  <text x="1000" y="370" class="content">需求，属于需要被激发需求的潜在客户。</text>
  
  <text x="1000" y="420" class="highlight">客户二的痛点则非常典型和集中：</text>
  <text x="1000" y="450" class="content">1. 健康隐忧：独居且有慢性病，缺乏日常的健康监测手段</text>
  <text x="1000" y="480" class="content">2. 通信不畅：2G手机无法使用，与外界及子女的联系中断</text>
  <text x="1000" y="510" class="content">3. 亲情缺失：子女不在身边，情感上需要关怀，安全上</text>
  <text x="1000" y="540" class="content">   也需要远程看护</text>
  <text x="1000" y="570" class="content">4. 经济顾虑：家庭条件一般，对新的消费支出较为敏感</text>
  
  <!-- 装饰元素 -->
  <circle cx="150" cy="800" r="80" fill="#3498db" opacity="0.1"/>
  <circle cx="1770" cy="800" r="80" fill="#e74c3c" opacity="0.1"/>
  
  <!-- 页面底部装饰线 -->
  <rect x="60" y="1020" width="1800" height="4" fill="#3498db"/>
</svg>
