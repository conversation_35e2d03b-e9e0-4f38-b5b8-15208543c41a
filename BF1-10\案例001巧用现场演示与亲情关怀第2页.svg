<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: 600; fill: #34495e; }
      .content { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: 600; fill: #e74c3c; }
      .section-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .success { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: 600; fill: #27ae60; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="60" y="60" width="1800" height="80" rx="10" class="section-bg"/>
  <text x="960" y="115" text-anchor="middle" class="title">解决方案</text>
  
  <!-- 解决方案内容 -->
  <rect x="60" y="180" width="1800" height="520" rx="10" class="section-bg"/>
  
  <text x="80" y="220" class="subtitle">针对两个客户的不同情况，营销团队采取了差异化的策略：</text>
  
  <!-- 客户一解决方案 -->
  <text x="80" y="280" class="highlight">1. 针对客户一：价值演示，眼见为实</text>
  <text x="100" y="320" class="content">当客户邻居无意中提及某地市正在使用政府同款的警用监控时，团队负责人立刻抓住时机，</text>
  <text x="100" y="350" class="content">强调这款产品的专业性和可靠性。随即，当场开封一台新监控设备，详细演示了其高清画质、</text>
  <text x="100" y="380" class="content">夜视、远程喊话等核心功能。直观的产品力展示迅速打动了客户，当场决定办理包含监控</text>
  <text x="100" y="410" class="content">服务的54元套餐。</text>
  
  <!-- 客户二解决方案 -->
  <text x="80" y="470" class="highlight">2. 针对客户二：服务先行，情感共鸣</text>
  <text x="100" y="510" class="content">团队主动为老人免费测量血压，以"适老服务"建立初步信任。在了解到老人的通信和健康</text>
  <text x="100" y="540" class="content">痛点后，首先提出以成本价为其更换一部智能手机，并推荐了经济实惠的39元基础套餐，</text>
  <text x="100" y="570" class="content">打消其经济顾虑。随后，营销的重点转向了"适老关怀平台"与监控功能的结合演示，生动</text>
  <text x="100" y="600" class="content">地展示了子女如何通过手机APP远程查看家中情况、与老人实时对话，甚至在老人长时间未</text>
  <text x="100" y="630" class="content">出门时收到预警短信。这种对"亲情看护"场景的描绘，深深触动了老人内心，最终成功将</text>
  <text x="100" y="660" class="content">订单从39元基础套餐升级为功能更全面的54元套餐。</text>
  
  <!-- 项目成果 -->
  <rect x="60" y="740" width="880" height="120" rx="10" fill="#d5f4e6" stroke="#27ae60" stroke-width: 2;/>
  <text x="80" y="780" class="subtitle">项目成果</text>
  <text x="80" y="820" class="success">成功为两户村民办理了54元数字乡村康养套餐</text>
  
  <!-- 装饰元素 -->
  <rect x="980" y="740" width="880" height="120" rx="10" fill="#fef9e7" stroke="#f39c12" stroke-width: 2;/>
  <text x="1000" y="780" class="subtitle">关键策略</text>
  <text x="1000" y="810" class="content">• 价值演示：眼见为实</text>
  <text x="1000" y="840" class="content">• 服务先行：情感共鸣</text>
  
  <!-- 页面底部装饰线 -->
  <rect x="60" y="1020" width="1800" height="4" fill="#3498db"/>
</svg>
