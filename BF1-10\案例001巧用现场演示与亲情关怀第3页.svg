<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: 600; fill: #34495e; }
      .content { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: 600; fill: #e74c3c; }
      .section-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .insight { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: 600; fill: #8e44ad; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="60" y="60" width="1800" height="80" rx="10" class="section-bg"/>
  <text x="960" y="115" text-anchor="middle" class="title">复盘总结</text>
  
  <!-- 复盘总结内容 -->
  <rect x="60" y="180" width="1800" height="680" rx="10" class="section-bg"/>
  
  <text x="80" y="230" class="insight">本案例的核心价值在于揭示了在农村市场，销售的成功不仅依赖于产品本身，</text>
  <text x="80" y="270" class="insight">更在于如何将产品价值与客户的实际生活场景和情感需求紧密结合。</text>
  
  <!-- 关键洞察1 -->
  <rect x="100" y="320" width="1700" height="180" rx="8" fill="#e8f4fd" stroke="#3498db" stroke-width="2"/>
  <text x="120" y="360" class="highlight">对于需求不明确的客户</text>
  <text x="120" y="400" class="content">"眼见为实"的现场演示是创造需求、建立信任最有效的方式。当客户邻居提及政府同款</text>
  <text x="120" y="430" class="content">警用监控时，团队立刻抓住时机强调产品的专业性，并当场开封设备进行功能演示，直观</text>
  <text x="120" y="460" class="content">的产品力展示迅速打动了客户。</text>
  
  <!-- 关键洞察2 -->
  <rect x="100" y="520" width="1700" height="220" rx="8" fill="#fdf2e9" stroke="#e67e22" stroke-width="2"/>
  <text x="120" y="560" class="highlight">对于有实际困难的老年客户群体</text>
  <text x="120" y="600" class="content">应采取"服务先行，情感共鸣"的策略。通过免费健康检测等公益服务切入，精准挖掘其</text>
  <text x="120" y="630" class="content">在健康、安全、亲情方面的深层痛点，并将产品功能包装成能够弥补情感缺失、连接家人</text>
  <text x="120" y="660" class="content">关爱的解决方案。对"亲情看护"场景的生动描绘，深深触动了老人内心，成功实现了从</text>
  <text x="120" y="690" class="content">39元基础套餐到54元全功能套餐的升级。</text>
  
  <!-- 核心启示 -->
  <rect x="100" y="760" width="1700" height="80" rx="8" fill="#eaf2f8" stroke="#2980b9" stroke-width="2"/>
  <text x="960" y="810" text-anchor="middle" class="insight">往往能达到事半功倍的效果</text>
  
  <!-- 装饰元素 -->
  <circle cx="150" cy="950" r="60" fill="#3498db" opacity="0.1"/>
  <circle cx="1770" cy="950" r="60" fill="#e74c3c" opacity="0.1"/>
  
  <!-- 页面底部装饰线 -->
  <rect x="60" y="1020" width="1800" height="4" fill="#3498db"/>
</svg>
