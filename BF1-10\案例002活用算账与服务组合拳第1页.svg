<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: 600; fill: #34495e; }
      .content { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: 600; fill: #e74c3c; }
      .section-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .price { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #27ae60; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="60" y="60" width="1800" height="120" rx="10" class="section-bg"/>
  <text x="960" y="130" text-anchor="middle" class="title">案例二：活用"算账"与"服务"组合拳</text>
  <text x="960" y="170" text-anchor="middle" class="subtitle">实现异网高价值客户策反</text>
  
  <!-- 客户背景 -->
  <rect x="60" y="220" width="880" height="400" rx="10" class="section-bg"/>
  <text x="80" y="260" class="subtitle">客户背景</text>
  <text x="80" y="310" class="content">客户家庭正在使用竞争对手（友商）的宽带与手机号卡</text>
  <text x="80" y="340" class="content">服务。具体情况为：</text>
  
  <text x="80" y="390" class="highlight">• 宽带套餐：</text>
  <text x="280" y="390" class="price">138元/月</text>
  <text x="420" y="390" class="content">（名义价格）</text>
  
  <text x="80" y="430" class="highlight">• 手机卡：</text>
  <text x="220" y="430" class="price">68元/月</text>
  <text x="340" y="430" class="content">（妻子使用）</text>
  
  <text x="80" y="480" class="content">家庭有看护鸡圈的安防需求，但对更换运营商存在</text>
  <text x="80" y="510" class="content">销户麻烦、号码变更通知亲友繁琐等顾虑。</text>
  
  <text x="80" y="560" class="highlight">实际发现：</text>
  <text x="80" y="590" class="content">通过手机APP查询发现，138元的套餐月均实际消费</text>
  <text x="80" y="590" class="price">高达186元</text>
  
  <!-- 痛点分析 -->
  <rect x="980" y="220" width="880" height="400" rx="10" class="section-bg"/>
  <text x="1000" y="260" class="subtitle">痛点分析</text>
  
  <text x="1000" y="310" class="highlight">1. 费用痛点</text>
  <text x="1000" y="340" class="content">客户认为每月通信支出较高，特别是友商套餐存在</text>
  <text x="1000" y="370" class="content">"隐形消费"，费用不透明，感知不佳。</text>
  
  <text x="1000" y="420" class="highlight">2. 需求痛点</text>
  <text x="1000" y="450" class="content">现有的服务无法满足其"AI看护鸡圈"这一具体</text>
  <text x="1000" y="480" class="content">安防需求。</text>
  
  <text x="1000" y="530" class="highlight">3. 转换障碍</text>
  <text x="1000" y="560" class="content">担心更换运营商流程复杂，尤其是原号码注销和</text>
  <text x="1000" y="590" class="content">新号码更换后通知亲友的过程，怕麻烦。</text>
  
  <!-- 装饰元素 -->
  <circle cx="150" cy="800" r="80" fill="#3498db" opacity="0.1"/>
  <circle cx="1770" cy="800" r="80" fill="#e74c3c" opacity="0.1"/>
  
  <!-- 页面底部装饰线 -->
  <rect x="60" y="1020" width="1800" height="4" fill="#3498db"/>
</svg>
