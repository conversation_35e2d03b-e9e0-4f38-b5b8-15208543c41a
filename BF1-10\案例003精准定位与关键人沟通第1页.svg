<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: 600; fill: #34495e; }
      .content { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: 600; fill: #e74c3c; }
      .section-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .price { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #27ae60; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="60" y="60" width="1800" height="120" rx="10" class="section-bg"/>
  <text x="960" y="130" text-anchor="middle" class="title">案例三：精准定位与关键人沟通</text>
  <text x="960" y="170" text-anchor="middle" class="subtitle">高效转化异网老年用户</text>
  
  <!-- 客户背景 -->
  <rect x="60" y="220" width="880" height="400" rx="10" class="section-bg"/>
  <text x="80" y="260" class="subtitle">客户背景</text>
  <text x="80" y="310" class="content">一对在家务农的老年夫妇，子女均已在城区成家，</text>
  <text x="80" y="340" class="content">大约两三个月才回家一次。</text>
  
  <text x="80" y="390" class="highlight">现状：</text>
  <text x="80" y="420" class="content">• 家中使用友商宽带服务</text>
  <text x="80" y="450" class="content">• 套餐费用由儿子远程支付</text>
  <text x="80" y="480" class="content">• 老两口均有高血压病史</text>
  <text x="80" y="510" class="content">• 家中未安装任何安防监控设备</text>
  
  <text x="80" y="560" class="highlight">费用情况：</text>
  <text x="80" y="590" class="price">58元套餐，月均实际扣费64-70元</text>
  
  <!-- 痛点分析 -->
  <rect x="980" y="220" width="880" height="400" rx="10" class="section-bg"/>
  <text x="1000" y="260" class="subtitle">痛点分析</text>
  
  <text x="1000" y="310" class="highlight">1. 决策者分离</text>
  <text x="1000" y="340" class="content">宽带的使用者（父母）和付费者（儿子）不是同一人，</text>
  <text x="1000" y="370" class="content">直接向老人营销缴费业务是无效的。</text>
  
  <text x="1000" y="420" class="highlight">2. 潜在需求</text>
  <text x="1000" y="450" class="content">老人有健康监测的需求，同时作为长期在家的老人，</text>
  <text x="1000" y="480" class="content">家庭安防也是一个潜在的刚需。</text>
  
  <text x="1000" y="530" class="highlight">3. 信息不对称</text>
  <text x="1000" y="560" class="content">付费的儿子不清楚父母家中宽带的实际消费情况，</text>
  <text x="1000" y="590" class="content">也不完全了解父母的日常生活细节和潜在风险。</text>
  
  <!-- 装饰元素 -->
  <circle cx="150" cy="800" r="80" fill="#3498db" opacity="0.1"/>
  <circle cx="1770" cy="800" r="80" fill="#e74c3c" opacity="0.1"/>
  
  <!-- 页面底部装饰线 -->
  <rect x="60" y="1020" width="1800" height="4" fill="#3498db"/>
</svg>
