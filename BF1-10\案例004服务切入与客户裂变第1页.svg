<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: 600; fill: #34495e; }
      .content { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: 600; fill: #e74c3c; }
      .section-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .price { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #27ae60; }
      .step { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3498db; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="60" y="60" width="1800" height="120" rx="10" class="section-bg"/>
  <text x="960" y="130" text-anchor="middle" class="title">案例四：服务切入与客户裂变</text>
  <text x="960" y="170" text-anchor="middle" class="subtitle">实现企业员工套餐双重突破</text>
  
  <!-- 客户背景 -->
  <rect x="60" y="220" width="880" height="300" rx="10" class="section-bg"/>
  <text x="80" y="260" class="subtitle">客户背景</text>
  <text x="80" y="310" class="content">一位企业员工，同时也是我方的长期老客户。</text>
  <text x="80" y="350" class="content">在办理业务的过程中，他还介绍了一位有高端</text>
  <text x="80" y="380" class="content">手机置换需求的同事。</text>
  
  <text x="80" y="430" class="highlight">现状：</text>
  <text x="80" y="460" class="content">• 月消费约</text>
  <text x="240" y="460" class="price">89元</text>
  <text x="80" y="490" class="content">• 套餐可能存在优化空间</text>
  
  <!-- 痛点分析 -->
  <rect x="980" y="220" width="880" height="300" rx="10" class="section-bg"/>
  <text x="1000" y="260" class="subtitle">痛点分析</text>
  
  <text x="1000" y="310" class="highlight">客户一：</text>
  <text x="1000" y="340" class="content">潜在价值未被挖掘，其套餐可能存在优化空间，</text>
  <text x="1000" y="370" class="content">或可以通过稍加费用享受更多价值。</text>
  
  <text x="1000" y="420" class="highlight">客户二（同事）：</text>
  <text x="1000" y="450" class="content">有明确的购买新款小米折叠屏手机的需求，</text>
  <text x="1000" y="480" class="content">正在寻找性价比最高的购买方案。</text>
  
  <!-- 解决方案 -->
  <rect x="60" y="560" width="1800" height="340" rx="10" class="section-bg"/>
  <text x="80" y="600" class="subtitle">解决方案</text>
  
  <!-- 步骤1 -->
  <rect x="100" y="640" width="1700" height="120" rx="8" fill="#e8f4fd" stroke="#3498db" stroke-width="2"/>
  <text x="120" y="670" class="step">1. 服务切入，精准推荐</text>
  <text x="120" y="700" class="content">以免费为手机贴膜的服务为契机，与老客户建立轻松的沟通氛围。在服务过程中，自然地</text>
  <text x="120" y="730" class="content">询问其目前的资费情况，并为其进行了全家通信消费的"笔算"。通过对比，发现99元的</text>
  <text x="120" y="760" class="content">"社保合约"套餐（针对特定企业员工的优惠套餐）对客户更具吸引力。</text>
  
  <!-- 步骤2 -->
  <rect x="100" y="780" width="1700" height="100" rx="8" fill="#fdf2e9" stroke="#e67e22" stroke-width="2"/>
  <text x="120" y="810" class="step">2. 口碑裂变，价值驱动</text>
  <text x="120" y="840" class="content">在第一位客户办理完业务后，其同事被一同吸引过来。这位同事看中了价值较高的299元</text>
  <text x="120" y="870" class="content">档位套餐，因为该套餐能够提供极具吸引力的小米新款手机补贴。</text>
  
  <!-- 页面底部装饰线 -->
  <rect x="60" y="1020" width="1800" height="4" fill="#3498db"/>
</svg>
