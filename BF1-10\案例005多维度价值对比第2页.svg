<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: 600; fill: #34495e; }
      .content { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: 600; fill: #e74c3c; }
      .section-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .step { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3498db; }
      .success { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: 600; fill: #27ae60; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="60" y="60" width="1800" height="80" rx="10" class="section-bg"/>
  <text x="960" y="115" text-anchor="middle" class="title">解决方案</text>
  
  <!-- 解决方案内容 -->
  <rect x="60" y="180" width="1800" height="600" rx="10" class="section-bg"/>
  
  <!-- 步骤1 -->
  <rect x="100" y="220" width="1700" height="100" rx="8" fill="#e8f4fd" stroke="#3498db" stroke-width="2"/>
  <text x="120" y="250" class="step">1. 精准匹配</text>
  <text x="120" y="280" class="content">根据客户的企业报销档位，直接推荐了与之匹配的299元套餐，确保客户在不增加个人</text>
  <text x="120" y="310" class="content">实际支出的前提下进行方案切换。</text>
  
  <!-- 步骤2 -->
  <rect x="100" y="340" width="1700" height="120" rx="8" fill="#fdf2e9" stroke="#e67e22" stroke-width="2"/>
  <text x="120" y="370" class="step">2. 六维对比法</text>
  <text x="120" y="400" class="content">营销人员没有进行单点推销，而是从价格、流量、语音、宽带、副卡、手机直降六个维度，</text>
  <text x="120" y="430" class="content">制作了清晰的对比表，全面展示了我方套餐的压倒性优势，特别是友商完全不具备的</text>
  <text x="120" y="460" class="content">"手机直降"优惠。</text>
  
  <!-- 步骤3 -->
  <rect x="100" y="480" width="1700" height="100" rx="8" fill="#eafaf1" stroke="#27ae60" stroke-width="2"/>
  <text x="120" y="510" class="step">3. 信任加固</text>
  <text x="120" y="540" class="content">主动引导客户在电商平台（京东）上查询同款手机的零售价，通过线上比价，让客户亲眼</text>
  <text x="120" y="570" class="content">证实我方套餐提供的终端优惠是真实且力度巨大的。</text>
  
  <!-- 步骤4 -->
  <rect x="100" y="600" width="1700" height="160" rx="8" fill="#f4ecf7" stroke="#8e44ad" stroke-width="2"/>
  <text x="120" y="630" class="step">4. 扫除障碍</text>
  <text x="120" y="660" class="content">针对客户的携转顾虑，团队提供了"保姆式"服务。首先，主动帮助客户致电友商客服，</text>
  <text x="120" y="690" class="content">解决解约遇到的难题。其次，陪同客户登录友商APP，调出其近半年的流量、语音使用</text>
  <text x="120" y="720" class="content">记录，用实际数据打消了客户对新套餐"不够用"的焦虑。</text>
  
  <!-- 项目成果 -->
  <rect x="60" y="820" width="1800" height="120" rx="10" fill="#d5f4e6" stroke="#27ae60" stroke-width="3"/>
  <text x="80" y="860" class="subtitle">项目成果</text>
  <text x="80" y="900" class="success">成功策反友商用户，客户携号转网办理了299元融合套餐</text>
  
  <!-- 页面底部装饰线 -->
  <rect x="60" y="1020" width="1800" height="4" fill="#3498db"/>
</svg>
