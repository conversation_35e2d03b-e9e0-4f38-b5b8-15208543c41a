<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: 600; fill: #34495e; }
      .content { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: 600; fill: #e74c3c; }
      .section-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .price { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #27ae60; }
      .step { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3498db; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="60" y="60" width="1800" height="120" rx="10" class="section-bg"/>
  <text x="960" y="130" text-anchor="middle" class="title">案例六：巧设服务场景，放大痛点</text>
  <text x="960" y="170" text-anchor="middle" class="subtitle">攻克顽固用户</text>
  
  <!-- 客户背景 -->
  <rect x="60" y="220" width="880" height="300" rx="10" class="section-bg"/>
  <text x="80" y="260" class="subtitle">客户背景</text>
  <text x="80" y="310" class="content">某大型企业园区内的一名员工王女士，是友商</text>
  <text x="80" y="340" class="content">（LT）的老用户。</text>
  
  <text x="80" y="390" class="highlight">现状：</text>
  <text x="80" y="420" class="content">• 每月话费高达</text>
  <text x="320" y="420" class="price">110元</text>
  <text x="80" y="450" class="content">• 对友商服务极为不满</text>
  <text x="80" y="480" class="content">• 对"套餐乱收费"积怨已久</text>
  
  <!-- 痛点分析 -->
  <rect x="980" y="220" width="880" height="300" rx="10" class="section-bg"/>
  <text x="1000" y="260" class="subtitle">痛点分析</text>
  
  <text x="1000" y="310" class="highlight">核心痛点：</text>
  <text x="1000" y="340" class="content">对友商的服务极为不满，尤其是对"套餐乱收费"</text>
  <text x="1000" y="370" class="content">的现象积怨已久。</text>
  
  <text x="1000" y="420" class="highlight">情绪状态：</text>
  <text x="1000" y="450" class="content">• 有强烈的被"套路"感</text>
  <text x="1000" y="480" class="content">• 对运营商普遍不信任</text>
  <text x="1000" y="510" class="content">• 需要情绪疏导和理解</text>
  
  <!-- 解决方案 -->
  <rect x="60" y="560" width="1800" height="340" rx="10" class="section-bg"/>
  <text x="80" y="600" class="subtitle">解决方案</text>
  
  <!-- 步骤1 -->
  <rect x="100" y="640" width="1700" height="100" rx="8" fill="#e8f4fd" stroke="#3498db" stroke-width="2"/>
  <text x="120" y="670" class="step">1. 选址布局，创造触点</text>
  <text x="120" y="700" class="content">团队敏锐地观察到园区员工午饭后有在食堂附近散步的习惯。因此，他们选择在客流量</text>
  <text x="120" y="730" class="content">最高的食堂门口设置服务帐篷，并利用炎热天气，将帐篷下的阴凉处作为天然的"引流"工具。</text>
  
  <!-- 步骤2 -->
  <rect x="100" y="760" width="1700" height="120" rx="8" fill="#fdf2e9" stroke="#e67e22" stroke-width="2"/>
  <text x="120" y="790" class="step">2. 服务先行，破冰建联</text>
  <text x="120" y="820" class="content">当王女士路过时，团队成员主动邀请她到帐篷下乘凉休息，提供人性化的关怀，而非直接</text>
  <text x="120" y="850" class="content">营销。在轻松的交谈中，引导王女士吐露了对友商乱收费现象的抱怨。</text>
  
  <!-- 页面底部装饰线 -->
  <rect x="60" y="1020" width="1800" height="4" fill="#3498db"/>
</svg>
