<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: 600; fill: #34495e; }
      .content { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: 600; fill: #e74c3c; }
      .section-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .price { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #27ae60; }
      .step { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3498db; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="60" y="60" width="1800" height="120" rx="10" class="section-bg"/>
  <text x="960" y="130" text-anchor="middle" class="title">案例七：活用公益活动与上门测速</text>
  <text x="960" y="170" text-anchor="middle" class="subtitle">精准转化社区宽带用户</text>
  
  <!-- 客户背景 -->
  <rect x="60" y="220" width="880" height="380" rx="10" class="section-bg"/>
  <text x="80" y="260" class="subtitle">客户背景</text>
  <text x="80" y="310" class="content">一位在小区内独居调理身体的用户，其子女在外地</text>
  <text x="80" y="340" class="content">上学和工作。</text>
  
  <text x="80" y="390" class="highlight">现状：</text>
  <text x="80" y="420" class="content">• 家中新安装了友商（YD）的宽带</text>
  <text x="80" y="450" class="content">• 网络体验极差</text>
  <text x="80" y="480" class="content">• 实际网速仅有</text>
  <text x="320" y="480" class="price">50M左右</text>
  <text x="80" y="510" class="content">• 刚办理一个多月</text>
  
  <text x="80" y="560" class="highlight">影响：</text>
  <text x="80" y="590" class="content">糟糕的网络体验已经影响到了用户的个人心情</text>
  
  <!-- 痛点分析 -->
  <rect x="980" y="220" width="880" height="380" rx="10" class="section-bg"/>
  <text x="1000" y="260" class="subtitle">痛点分析</text>
  
  <text x="1000" y="310" class="highlight">1. 网络质量差</text>
  <text x="1000" y="340" class="content">友商宽带实际网速仅有50M左右，无法满足现代</text>
  <text x="1000" y="370" class="content">家庭，特别是子女回家期间的上网需求。</text>
  
  <text x="1000" y="420" class="highlight">2. 负面情绪</text>
  <text x="1000" y="450" class="content">糟糕的网络体验已经影响到了用户的个人心情，</text>
  <text x="1000" y="480" class="content">对于正在调理身体的用户来说，这是一个亟待</text>
  <text x="1000" y="510" class="content">解决的问题。</text>
  
  <text x="1000" y="560" class="highlight">3. 转换成本</text>
  <text x="1000" y="590" class="content">用户刚办理友商宽带一个多月，存在一定的</text>
  <text x="1000" y="620" class="content">沉没成本顾虑。</text>
  
  <!-- 解决方案开始 -->
  <rect x="60" y="640" width="1800" height="240" rx="10" class="section-bg"/>
  <text x="80" y="680" class="subtitle">解决方案</text>
  
  <!-- 步骤1 -->
  <rect x="100" y="720" width="1700" height="80" rx="8" fill="#e8f4fd" stroke="#3498db" stroke-width="2"/>
  <text x="120" y="750" class="step">1. 公益引流</text>
  <text x="120" y="780" class="content">在小区门口开展"公益进社区，免费测血压"活动，以健康服务的名义吸引居民关注，</text>
  <text x="120" y="810" class="content">成功接触到目标用户。</text>
  
  <!-- 页面底部装饰线 -->
  <rect x="60" y="1020" width="1800" height="4" fill="#3498db"/>
</svg>
