<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: 600; fill: #34495e; }
      .content { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: 600; fill: #e74c3c; }
      .section-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .step { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3498db; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="60" y="60" width="1800" height="120" rx="10" class="section-bg"/>
  <text x="960" y="130" text-anchor="middle" class="title">案例八：以网络检测为名</text>
  <text x="960" y="170" text-anchor="middle" class="subtitle">挖掘多重需求并提供整合方案</text>
  
  <!-- 客户背景 -->
  <rect x="60" y="220" width="880" height="300" rx="10" class="section-bg"/>
  <text x="80" y="260" class="subtitle">客户背景</text>
  <text x="80" y="310" class="content">一位异网用户，家中网络不稳定，导致手机经常</text>
  <text x="80" y="340" class="content">自动切换到移动数据，产生额外流量费。</text>
  
  <text x="80" y="390" class="highlight">家庭情况：</text>
  <text x="80" y="420" class="content">• 家中老人的身体状况无法随时了解</text>
  <text x="80" y="450" class="content">• Wi-Fi信号覆盖不全且不稳定</text>
  <text x="80" y="480" class="content">• 每月产生额外的手机流量费用</text>
  
  <!-- 痛点分析 -->
  <rect x="980" y="220" width="880" height="300" rx="10" class="section-bg"/>
  <text x="1000" y="260" class="subtitle">痛点分析</text>
  
  <text x="1000" y="310" class="highlight">1. 网络痛点</text>
  <text x="1000" y="340" class="content">Wi-Fi信号覆盖不全且不稳定，导致使用体验差。</text>
  
  <text x="1000" y="380" class="highlight">2. 费用痛点</text>
  <text x="1000" y="410" class="content">网络问题直接导致了每月额外的手机流量溢出费用。</text>
  
  <text x="1000" y="450" class="highlight">3. 服务痛点</text>
  <text x="1000" y="480" class="content">经查询，发现其友商（YD）套餐曾存在用户不知情</text>
  <text x="1000" y="510" class="content">的变更情况，破坏了消费信任。</text>
  
  <!-- 解决方案 -->
  <rect x="60" y="560" width="1800" height="340" rx="10" class="section-bg"/>
  <text x="80" y="600" class="subtitle">解决方案</text>
  
  <!-- 步骤1 -->
  <rect x="100" y="640" width="1700" height="80" rx="8" fill="#e8f4fd" stroke="#3498db" stroke-width="2"/>
  <text x="120" y="670" class="step">1. 服务预约</text>
  <text x="120" y="700" class="content">以提供免费"网络检测服务"为由，获得上门服务的机会。</text>
  
  <!-- 步骤2 -->
  <rect x="100" y="740" width="1700" height="140" rx="8" fill="#fdf2e9" stroke="#e67e22" stroke-width="2"/>
  <text x="120" y="770" class="step">2. 痛点深挖</text>
  <text x="120" y="800" class="content">上门后，系统性地检查并指出了用户家中的Wi-Fi覆盖问题，并帮助用户算清了每月因此</text>
  <text x="120" y="830" class="content">多付的流量费。在查询套餐时，又发现了友商"不知情变更"的问题，进一步加深了用户</text>
  <text x="120" y="860" class="content">对友商的不满。</text>
  
  <!-- 页面底部装饰线 -->
  <rect x="60" y="1020" width="1800" height="4" fill="#3498db"/>
</svg>
