<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: 600; fill: #34495e; }
      .content { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: 600; fill: #e74c3c; }
      .section-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .step { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3498db; }
      .success { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: 600; fill: #27ae60; }
      .insight { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: 600; fill: #8e44ad; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 解决方案续 -->
  <rect x="60" y="60" width="1800" height="200" rx="10" class="section-bg"/>
  <text x="80" y="100" class="subtitle">解决方案（续）</text>
  
  <!-- 步骤3 -->
  <rect x="100" y="140" width="1700" height="100" rx="8" fill="#eafaf1" stroke="#27ae60" stroke-width="2"/>
  <text x="120" y="170" class="step">3. 整合方案</text>
  <text x="120" y="200" class="content">在全面挖掘出网络、费用、服务、亲情四大痛点后，营销人员没有逐一解决，而是提出了</text>
  <text x="120" y="230" class="content">一个"一揽子"解决方案：推荐"全屋Wi-Fi"彻底解决网络问题，加载"康养礼包"（内含</text>
  <text x="120" y="260" class="content">血压仪）满足对老人的关怀需求，并强调我方套餐政策长期稳定透明。</text>
  
  <!-- 项目成果 -->
  <rect x="60" y="300" width="1800" height="120" rx="10" fill="#d5f4e6" stroke="#27ae60" stroke-width="3"/>
  <text x="80" y="340" class="subtitle">项目成果</text>
  <text x="80" y="380" class="success">成功办理新手机、新宽带，并叠加了全屋Wi-Fi服务和血压仪</text>
  
  <!-- 复盘总结 -->
  <rect x="60" y="460" width="1800" height="440" rx="10" class="section-bg"/>
  <text x="80" y="500" class="subtitle">复盘总结</text>
  
  <text x="80" y="550" class="insight">这个案例展示了从单一痛点切入，进而挖掘和满足客户"隐藏需求"的能力。</text>
  
  <!-- 关键能力1 -->
  <rect x="100" y="590" width="1700" height="100" rx="8" fill="#e8f4fd" stroke="#3498db" stroke-width="2"/>
  <text x="120" y="620" class="highlight">专业诊断能力</text>
  <text x="120" y="650" class="content">优秀的营销人员如同医生，"望闻问切"，通过专业的检测服务，不仅要解决客户提出的</text>
  <text x="120" y="680" class="content">"主诉"（网络卡），更要发现其背后的"并发症"（流量费高、套餐有问题、关心老人）。</text>
  
  <!-- 关键能力2 -->
  <rect x="100" y="710" width="1700" height="100" rx="8" fill="#fdf2e9" stroke="#e67e22" stroke-width="2"/>
  <text x="120" y="740" class="highlight">整合解决方案能力</text>
  <text x="120" y="770" class="content">通过提供一个高度整合的解决方案，让客户感觉到"省心"和"超值"，从而实现从低价值</text>
  <text x="120" y="800" class="content">向高价值的转化。一次性解决多个痛点，提升客户满意度和成交价值。</text>
  
  <!-- 核心启示 -->
  <rect x="100" y="830" width="1700" height="80" rx="8" fill="#eafaf1" stroke="#27ae60" stroke-width="2"/>
  <text x="120" y="860" class="highlight">从"产品思维"转向"客户思维"</text>
  <text x="120" y="890" class="content">不是简单地推销单一产品，而是深度挖掘客户的综合需求，提供系统性的解决方案。</text>
  
  <!-- 页面底部装饰线 -->
  <rect x="60" y="1020" width="1800" height="4" fill="#3498db"/>
</svg>
