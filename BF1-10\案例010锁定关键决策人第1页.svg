<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: 600; fill: #34495e; }
      .content { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: 600; fill: #e74c3c; }
      .section-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .price { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #27ae60; }
      .step { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3498db; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题区域 -->
  <rect x="60" y="60" width="1800" height="120" rx="10" class="section-bg"/>
  <text x="960" y="130" text-anchor="middle" class="title">案例十：锁定关键决策人</text>
  <text x="960" y="170" text-anchor="middle" class="subtitle">实现存量家庭宽带升级</text>
  
  <!-- 客户背景 -->
  <rect x="60" y="220" width="880" height="380" rx="10" class="section-bg"/>
  <text x="80" y="260" class="subtitle">客户背景</text>
  <text x="80" y="310" class="content">一个电信老用户家庭，营销人员通过社区康养</text>
  <text x="80" y="340" class="content">活动接触到家里的老人。</text>
  
  <text x="80" y="390" class="highlight">现状：</text>
  <text x="80" y="420" class="content">• 原套餐为</text>
  <text x="240" y="420" class="price">99元</text>
  <text x="80" y="450" class="content">• 宽带为</text>
  <text x="200" y="450" class="price">300M</text>
  <text x="80" y="480" class="content">• 老人对家里的网络套餐情况不了解</text>
  <text x="80" y="510" class="content">• 不知道当前小区已具备千兆网络能力</text>
  
  <text x="80" y="560" class="highlight">升级潜力：</text>
  <text x="80" y="590" class="content">小区已支持千兆网络，存在升级空间</text>
  
  <!-- 痛点分析 -->
  <rect x="980" y="220" width="880" height="380" rx="10" class="section-bg"/>
  <text x="1000" y="260" class="subtitle">痛点分析</text>
  
  <text x="1000" y="310" class="highlight">1. 信息差</text>
  <text x="1000" y="340" class="content">老人对家里的网络套餐情况不了解，也不知道</text>
  <text x="1000" y="370" class="content">当前小区已具备千兆网络能力。</text>
  
  <text x="1000" y="420" class="highlight">2. 决策权</text>
  <text x="1000" y="450" class="content">老人并非网络套餐的最终决策者，其子女才是</text>
  <text x="1000" y="480" class="content">真正的决策人。</text>
  
  <text x="1000" y="530" class="highlight">3. 潜在需求</text>
  <text x="1000" y="560" class="content">随着家庭智能设备增多，300M宽带可能已无法</text>
  <text x="1000" y="590" class="content">满足最佳体验，存在升级千兆和优化全屋网络</text>
  <text x="1000" y="620" class="content">覆盖的需求。</text>
  
  <!-- 解决方案开始 -->
  <rect x="60" y="640" width="1800" height="240" rx="10" class="section-bg"/>
  <text x="80" y="680" class="subtitle">解决方案</text>
  
  <!-- 步骤1 -->
  <rect x="100" y="720" width="1700" height="80" rx="8" fill="#e8f4fd" stroke="#3498db" stroke-width="2"/>
  <text x="120" y="750" class="step">1. 初步接触</text>
  <text x="120" y="780" class="content">借助康养礼包现场为老人测血压，建立良好关系，并留下联系方式，为后续跟进埋下伏笔。</text>
  
  <!-- 步骤2 -->
  <rect x="100" y="820" width="1700" height="80" rx="8" fill="#fdf2e9" stroke="#e67e22" stroke-width="2"/>
  <text x="120" y="850" class="step">2. 用户画像</text>
  <text x="120" y="880" class="content">通过内部系统查询该用户的套餐信息，提前做好了用户画像，了解到其宽带速率有提升空间。</text>
  
  <!-- 页面底部装饰线 -->
  <rect x="60" y="1020" width="1800" height="4" fill="#3498db"/>
</svg>
